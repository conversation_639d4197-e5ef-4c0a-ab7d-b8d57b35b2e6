import requests
import json

def get_decision_ids(page_number=1, page_size=100):
    url = 'https://karararama.yargitay.gov.tr/aramadetaylist'
    headers = {'Content-Type': 'application/json'}

    payload = {
        "data": {
            "arananKelime": "",
            "yargitayMah": "Büyük Genel Kurulu",
            "hukuk": "23. Hukuk Dairesi",
            "ceza": "23. Ceza Dairesi",
            "esasYil": "",
            "esasIlkSiraNo": "",
            "esasSonSiraNo": "",
            "kararYil": "",
            "kararIlkSiraNo": "",
            "kararSonSiraNo": "",
            "baslangicTarihi": "",
            "bitisTarihi": "",
            "siralama": "1",
            "siralamaDirection": "desc",
            "birimYrgKurulDaire": "Hukuk Genel Kurulu+Ceza Genel Kurulu+Ceza Daireleri Başkanlar Kurulu+Hukuk Daireleri Başkanlar Kurulu+Büyük Genel Kurulu",
            "birimYrgHukukDaire": "+".join([f"{i}. Hukuk Dairesi" for i in range(1, 24)]),
            "birimYrgCezaDaire": "+".join([f"{i}. Ceza Dairesi" for i in range(1, 24)]),
            "pageSize": page_size,
            "pageNumber": page_number
        }
    }

    response = requests.post(url, headers=headers, json=payload)
    response.raise_for_status()

    data = response.json()
    items = data["data"]["data"]
    return [item["id"] for item in items]

if __name__ == "__main__":
    print("1. Karar ID'lerini getirme testi başlıyor...")
    try:
        ids = get_decision_ids(page_number=1, page_size=10)
        print(f"✅ Başarılı! {len(ids)} adet karar ID'si alındı:")
        for i, decision_id in enumerate(ids[:5], 1):
            print(f"  {i}. {decision_id}")
        if len(ids) > 5:
            print(f"  ... ve {len(ids)-5} tane daha")
    except Exception as e:
        print(f"❌ Hata: {e}")
