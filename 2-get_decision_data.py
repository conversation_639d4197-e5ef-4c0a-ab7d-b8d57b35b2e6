import requests

def get_decision_html(decision_id):
    url = f'https://karararama.yargitay.gov.tr/getDokuman?id={decision_id}'
    response = requests.get(url)
    response.raise_for_status()
    data = response.json()
    return data.get("data")

if __name__ == "__main__":
    print("2. Karar HTML verisi getirme testi başlıyor...")

    # Önce bir karar ID'si alalım
    try:
        from importlib import import_module
        get_ids_module = import_module("1-get_decision_id")
        ids = get_ids_module.get_decision_ids(page_number=1, page_size=5)

        if ids:
            test_id = ids[0]
            print(f"Test için karar ID: {test_id}")

            html = get_decision_html(test_id)
            if html:
                print(f"✅ Başarılı! HTML verisi alındı (uzunluk: {len(html)} karakter)")
                print(f"İlk 200 karakter: {html[:200]}...")
            else:
                print("❌ HTML verisi boş geldi")
        else:
            print("❌ Test için karar ID'si bulunamadı")

    except Exception as e:
        print(f"❌ Hata: {e}")
