from bs4 import BeautifulSoup
import re

def parse_decision_html(html):
    soup = BeautifulSoup(html, "html.parser")

    # İlk satır: <PERSON>re + Esas/Karar
    bold_lines = soup.find_all('b')
    header_text = bold_lines[0].get_text(strip=True) if bold_lines else ""

    # Örnek: "10. Hukuk Dairesi       2025/6458 E.  ,  2025/6461 K."
    daire_match = re.match(r'(.+Dairesi)', header_text)
    esas_match = re.search(r'(\d{4}/\d+ E\.)', header_text)
    karar_match = re.search(r'(\d{4}/\d+ K\.)', header_text)

    daire = daire_match.group(1) if daire_match else ""
    esas_no = esas_match.group(1).replace(" E.", "") if esas_match else ""
    karar_no = karar_match.group(1).replace(" K.", "") if karar_match else ""

    # Tam metin
    paragraphs = soup.find_all('p')
    full_text = "\n".join([p.get_text(separator="\n", strip=True) for p in paragraphs])

    return {
        "daire": daire,
        "esas_no": esas_no,
        "karar_no": karar_no,
        "text": full_text
    }

if __name__ == "__main__":
    print("3. HTML parsing testi başlıyor...")

    try:
        from importlib import import_module

        # Önce karar ID'si alalım
        get_ids_module = import_module("1-get_decision_id")
        ids = get_ids_module.get_decision_ids(page_number=1, page_size=3)

        if ids:
            # Sonra HTML verisini alalım
            get_data_module = import_module("2-get_decision_data")
            test_id = ids[0]
            html = get_data_module.get_decision_html(test_id)

            if html:
                # HTML'i parse edelim
                parsed = parse_decision_html(html)
                print(f"✅ Başarılı! HTML parse edildi:")
                print(f"  Daire: {parsed['daire']}")
                print(f"  Esas No: {parsed['esas_no']}")
                print(f"  Karar No: {parsed['karar_no']}")
                print(f"  Metin uzunluğu: {len(parsed['text'])} karakter")
                print(f"  İlk 100 karakter: {parsed['text'][:100]}...")
            else:
                print("❌ HTML verisi alınamadı")
        else:
            print("❌ Test için karar ID'si bulunamadı")

    except Exception as e:
        print(f"❌ Hata: {e}")
