import time
import json
import os
from tqdm import tqdm
from database import db_manager, init_database

def crawl_and_save(start_page=1, end_page=5, output_file="yargitay_kararlar.jsonl", save_to_db=True):
    from importlib import import_module

    # Initialize database if saving to DB
    if save_to_db:
        try:
            init_database()
            print("✅ Database initialized successfully")
        except Exception as e:
            print(f"❌ Database initialization failed: {e}")
            print("Continuing with file-only saving...")
            save_to_db = False

    # Diğer modülleri import et
    get_ids_module = import_module("1-get_decision_id")
    get_data_module = import_module("2-get_decision_data")
    parse_module = import_module("3-parse_html")

    # Statistics
    total_processed = 0
    total_saved_to_db = 0
    total_saved_to_file = 0
    total_errors = 0

    # Ensure output directory exists
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    with open(output_file, "w", encoding="utf-8") as f:
        for page in range(start_page, end_page+1):
            print(f"📄 Fetching page {page}...")
            try:
                ids = get_ids_module.get_decision_ids(page)
                print(f"Found {len(ids)} decisions on page {page}")
            except Exception as e:
                print(f"❌ Error getting IDs from page {page}: {e}")
                continue

            for decision_id in tqdm(ids, desc=f"Page {page}"):
                total_processed += 1
                try:
                    html = get_data_module.get_decision_html(decision_id)
                    if not html:
                        continue

                    parsed = parse_module.parse_decision_html(html)
                    parsed["id"] = decision_id

                    # Save to database
                    if save_to_db:
                        try:
                            saved_decision = db_manager.save_decision(parsed)
                            if saved_decision:
                                total_saved_to_db += 1
                        except Exception as e:
                            print(f"❌ Database save error for {decision_id}: {e}")

                    # Save to file
                    try:
                        f.write(json.dumps(parsed, ensure_ascii=False) + "\n")
                        total_saved_to_file += 1
                    except Exception as e:
                        print(f"❌ File save error for {decision_id}: {e}")

                except Exception as e:
                    print(f"❌ Error fetching/parsing decision {decision_id}: {e}")
                    total_errors += 1

                time.sleep(0.2)  # siteyi çok zorlamamak için

            # Print page statistics
            if save_to_db:
                db_count = db_manager.get_decisions_count()
                print(f"📊 Page {page} completed. Total in database: {db_count}")

    # Print final statistics
    print("\n" + "="*50)
    print("📊 SCRAPING COMPLETED - FINAL STATISTICS")
    print("="*50)
    print(f"Total decisions processed: {total_processed}")
    print(f"Successfully saved to file: {total_saved_to_file}")
    if save_to_db:
        print(f"Successfully saved to database: {total_saved_to_db}")
        print(f"Total decisions in database: {db_manager.get_decisions_count()}")
    print(f"Errors encountered: {total_errors}")
    print(f"Output file: {output_file}")
    print("="*50)

if __name__ == "__main__":
    print("4. Tam süreç testi başlıyor...")
    print("Bu test 1 sayfa veri alıp JSON dosyasına ve veritabanına kaydedecek...")

    try:
        output_file = "output/test_yargitay_kararlar.jsonl"
        crawl_and_save(start_page=1, end_page=1, output_file=output_file, save_to_db=True)

        # Dosyayı kontrol et
        if os.path.exists(output_file):
            with open(output_file, "r", encoding="utf-8") as f:
                lines = f.readlines()

            print(f"✅ Başarılı! {len(lines)} adet karar {output_file} dosyasına kaydedildi")

            if lines:
                # İlk kaydı göster
                first_record = json.loads(lines[0])
                print("İlk kayıt örneği:")
                print(f"  ID: {first_record.get('id', 'N/A')}")
                print(f"  Daire: {first_record.get('daire', 'N/A')}")
                print(f"  Esas No: {first_record.get('esas_no', 'N/A')}")
                print(f"  Karar No: {first_record.get('karar_no', 'N/A')}")
                print(f"  Metin uzunluğu: {len(first_record.get('text', ''))} karakter")

        # Database statistics
        try:
            db_count = db_manager.get_decisions_count()
            print(f"📊 Veritabanında toplam karar sayısı: {db_count}")

            recent_decisions = db_manager.get_recent_decisions(3)
            if recent_decisions:
                print("Son eklenen kararlar:")
                for decision in recent_decisions:
                    print(f"  - {decision.decision_id}: {decision.daire}")
        except Exception as e:
            print(f"❌ Veritabanı istatistik hatası: {e}")

    except Exception as e:
        print(f"❌ Hata: {e}")
