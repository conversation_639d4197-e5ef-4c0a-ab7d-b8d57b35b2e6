import time
import json
from tqdm import tqdm

def crawl_and_save(start_page=1, end_page=5, output_file="yargitay_kararlar.jsonl"):
    from importlib import import_module

    # Diğer modülleri import et
    get_ids_module = import_module("1-get_decision_id")
    get_data_module = import_module("2-get_decision_data")
    parse_module = import_module("3-parse_html")

    with open(output_file, "w", encoding="utf-8") as f:
        for page in range(start_page, end_page+1):
            print(f"Fetching page {page}...")
            try:
                ids = get_ids_module.get_decision_ids(page)
            except Exception as e:
                print(f"Error getting IDs: {e}")
                continue

            for decision_id in tqdm(ids):
                try:
                    html = get_data_module.get_decision_html(decision_id)
                    if not html:
                        continue
                    parsed = parse_module.parse_decision_html(html)
                    parsed["id"] = decision_id
                    f.write(json.dumps(parsed, ensure_ascii=False) + "\n")
                except Exception as e:
                    print(f"Error fetching/parsing decision {decision_id}: {e}")
                time.sleep(0.2)  # siteyi çok zorlamamak için

if __name__ == "__main__":
    print("4. Tam süreç testi başlıyor...")
    print("Bu test 1 sayfa veri alıp JSON dosyasına kaydedecek...")

    try:
        output_file = "test_yargitay_kararlar.jsonl"
        crawl_and_save(start_page=1, end_page=1, output_file=output_file)

        # Dosyayı kontrol et
        with open(output_file, "r", encoding="utf-8") as f:
            lines = f.readlines()

        print(f"✅ Başarılı! {len(lines)} adet karar {output_file} dosyasına kaydedildi")

        if lines:
            # İlk kaydı göster
            first_record = json.loads(lines[0])
            print("İlk kayıt örneği:")
            print(f"  ID: {first_record.get('id', 'N/A')}")
            print(f"  Daire: {first_record.get('daire', 'N/A')}")
            print(f"  Esas No: {first_record.get('esas_no', 'N/A')}")
            print(f"  Karar No: {first_record.get('karar_no', 'N/A')}")
            print(f"  Metin uzunluğu: {len(first_record.get('text', ''))} karakter")

    except Exception as e:
        print(f"❌ Hata: {e}")
