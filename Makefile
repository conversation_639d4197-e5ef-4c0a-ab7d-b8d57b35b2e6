# Supreme Court Decisions Scraper - Makefile

.PHONY: help build up down logs shell clean test run-local install-deps

# Default target
help: ## Show this help message
	@echo "Supreme Court Decisions Scraper"
	@echo "Available commands:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Docker commands
build: ## Build the Docker image
	docker-compose build

up: ## Start the scraper in detached mode
	docker-compose up -d scraper

run: ## Run the scraper (foreground)
	docker-compose up scraper

down: ## Stop and remove containers
	docker-compose down

logs: ## Show logs from the scraper
	docker-compose logs -f scraper

shell: ## Open a shell in the scraper container
	docker-compose run --rm scraper-dev

dev: ## Start development environment with shell access
	docker-compose --profile dev up -d scraper-dev
	docker-compose --profile dev exec scraper-dev /bin/bash

# Utility commands
clean: ## Clean up Docker resources
	docker-compose down -v
	docker system prune -f
	docker volume prune -f

test: ## Run individual test modules
	@echo "Running module tests..."
	docker-compose run --rm scraper python 1-get_decision_id.py
	docker-compose run --rm scraper python 2-get_decision_data.py
	docker-compose run --rm scraper python 3-parse_html.py
	docker-compose run --rm scraper python 4-save_for_json.py

# Local development commands (without Docker)
install-deps: ## Install Python dependencies locally
	pip install -r requirements.txt

run-local: ## Run the scraper locally (requires Python and deps)
	python main.py

test-local: ## Run tests locally
	@echo "Running module tests locally..."
	python 1-get_decision_id.py
	python 2-get_decision_data.py
	python 3-parse_html.py
	python 4-save_for_json.py

# Data management
check-output: ## Check the output directory
	@echo "Output directory contents:"
	@ls -la ./output/ 2>/dev/null || echo "Output directory is empty or doesn't exist"

backup-data: ## Backup scraped data with timestamp
	@mkdir -p backups
	@timestamp=$$(date +%Y%m%d_%H%M%S); \
	if [ -d "./output" ] && [ "$$(ls -A ./output)" ]; then \
		tar -czf "backups/scraped_data_$$timestamp.tar.gz" ./output/; \
		echo "Data backed up to backups/scraped_data_$$timestamp.tar.gz"; \
	else \
		echo "No data to backup in ./output directory"; \
	fi

# Quick start
start: build up ## Build and start the scraper

restart: down up ## Restart the scraper

status: ## Show container status
	docker-compose ps
