# Supreme Court Decisions Scraper

A Python application that scrapes Turkish Supreme Court (Yargıtay) decisions and saves them in JSON format.

## Features

- Scrapes decision IDs from the Supreme Court database
- Fetches detailed HTML content for each decision
- Parses and extracts structured data (court chamber, case numbers, decision text)
- Saves data in JSONL format for easy processing
- Dockerized for easy deployment and consistent environment

## Project Structure

```
├── 1-get_decision_id.py    # Fetches decision IDs from search API
├── 2-get_decision_data.py  # Retrieves HTML content for decisions
├── 3-parse_html.py         # Parses HTML and extracts structured data
├── 4-save_for_json.py      # Orchestrates the full scraping process
├── main.py                 # Main entry point
├── requirements.txt        # Python dependencies
├── Dockerfile             # Docker image configuration
├── docker-compose.yml     # Docker Compose setup
├── Makefile              # Convenient commands for development
└── output/               # Directory for scraped data (created automatically)
```

## Quick Start with Docker

### Prerequisites
- Docker
- Docker Compose
- Make (optional, for convenient commands)

### Using Makefile (Recommended)

1. **Build and start the scraper:**
   ```bash
   make start
   ```

2. **View logs:**
   ```bash
   make logs
   ```

3. **Check scraped data:**
   ```bash
   make check-output
   ```

4. **Stop the scraper:**
   ```bash
   make down
   ```

### Manual Docker Commands

1. **Build the image:**
   ```bash
   docker-compose build
   ```

2. **Run the scraper:**
   ```bash
   docker-compose up scraper
   ```

3. **Run in background:**
   ```bash
   docker-compose up -d scraper
   ```

## Available Make Commands

Run `make help` to see all available commands:

- `make build` - Build the Docker image
- `make up` - Start the scraper in detached mode
- `make run` - Run the scraper in foreground
- `make down` - Stop and remove containers
- `make logs` - Show scraper logs
- `make shell` - Open a shell in the container
- `make dev` - Start development environment
- `make test` - Run individual module tests
- `make clean` - Clean up Docker resources
- `make backup-data` - Backup scraped data with timestamp

## Local Development

### Without Docker

1. **Install dependencies:**
   ```bash
   make install-deps
   # or manually: pip install -r requirements.txt
   ```

2. **Run the scraper:**
   ```bash
   make run-local
   # or manually: python main.py
   ```

3. **Run tests:**
   ```bash
   make test-local
   ```

### With Docker Development Environment

1. **Start development container:**
   ```bash
   make dev
   ```

2. **Run individual modules for testing:**
   ```bash
   make test
   ```

## Configuration

The scraper is configured to:
- Scrape pages 1-100 by default (configurable in `main.py`)
- Save data to `output/yargitay_kararlar.jsonl`
- Include a 0.2-second delay between requests to avoid overwhelming the server

To modify the scraping parameters, edit the `main.py` file:
```python
save_module.crawl_and_save(start_page=1, end_page=100, output_file=output_file)
```

## Output Format

The scraper saves data in JSONL format (one JSON object per line):

```json
{
  "id": "12345",
  "daire": "10. Hukuk Dairesi",
  "esas_no": "2025/6458",
  "karar_no": "2025/6461",
  "text": "Full decision text..."
}
```

## Data Management

- **Output Location:** All scraped data is saved to the `./output/` directory
- **Persistence:** The output directory is mounted as a Docker volume, so data persists between container restarts
- **Backup:** Use `make backup-data` to create timestamped backups

## Troubleshooting

1. **Permission Issues:**
   ```bash
   sudo chown -R $USER:$USER ./output/
   ```

2. **Container Issues:**
   ```bash
   make clean  # Clean up Docker resources
   make build  # Rebuild the image
   ```

3. **View Container Logs:**
   ```bash
   make logs
   ```

4. **Debug in Container:**
   ```bash
   make shell
   ```

## Notes

- The scraper includes rate limiting (0.2s delay) to be respectful to the source website
- Total pages available: ~95,941 (as noted in the code)
- Each page typically contains 100 decisions
- The scraper handles errors gracefully and continues processing
