version: '3.8'

services:
  scraper:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: supreme-court-scraper
    volumes:
      # Mount output directory to persist scraped data
      - ./output:/app/output
      # Mount source code for development (optional)
      - ./:/app
    environment:
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    # Override default command if needed
    # command: python main.py
    
  # Optional: Add a development service for interactive debugging
  scraper-dev:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: supreme-court-scraper-dev
    volumes:
      - ./:/app
      - ./output:/app/output
    environment:
      - PYTHONUNBUFFERED=1
    stdin_open: true
    tty: true
    command: /bin/bash
    profiles:
      - dev
