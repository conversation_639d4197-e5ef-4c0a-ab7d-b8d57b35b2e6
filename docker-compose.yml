version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: supreme-court-postgres
    environment:
      POSTGRES_DB: supreme_court_db
      POSTGRES_USER: scraper_user
      POSTGRES_PASSWORD: scraper_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5420:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U scraper_user -d supreme_court_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  scraper:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: supreme-court-scraper
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      # Mount output directory to persist scraped data
      - ./output:/app/output
      # Mount source code for development (optional)
      - ./:/app
    environment:
      - PYTHONUNBUFFERED=1
      - DATABASE_URL=********************************************************/supreme_court_db
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=supreme_court_db
      - DB_USER=scraper_user
      - DB_PASSWORD=scraper_password
    restart: unless-stopped
    # Override default command if needed
    # command: python main.py

  # Optional: Add a development service for interactive debugging
  scraper-dev:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: supreme-court-scraper-dev
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./:/app
      - ./output:/app/output
    environment:
      - PYTHONUNBUFFERED=1
      - DATABASE_URL=********************************************************/supreme_court_db
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=supreme_court_db
      - DB_USER=scraper_user
      - DB_PASSWORD=scraper_password
    stdin_open: true
    tty: true
    command: /bin/bash
    profiles:
      - dev

  # Optional: pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: supreme-court-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    ports:
      - "8080:80"
    depends_on:
      - postgres
    profiles:
      - admin

volumes:
  postgres_data:
