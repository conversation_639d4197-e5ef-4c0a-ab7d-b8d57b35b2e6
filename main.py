import os
from importlib import import_module
from database import db_manager

if __name__ == "__main__":
    print("🚀 Supreme Court Decisions Scraper başlıyor...")
    print("Tüm süreci çalıştırıp 100 sayfa veri alacak...")
    print("Veriler hem dosyaya hem de PostgreSQL veritabanına kaydedilecek...")

    # Create output directory if it doesn't exist
    output_dir = "output"
    os.makedirs(output_dir, exist_ok=True)

    # Output file path
    output_file = os.path.join(output_dir, "yargitay_kararlar.jsonl")

    #95941 pages total - running 100 pages for this session
    try:
        save_module = import_module("4-save_for_json")
        save_module.crawl_and_save(
            start_page=1,
            end_page=2,  # Test için sadece 2 sayfa
            output_file=output_file,
            save_to_db=True
        )

        print("\n" + "="*60)
        print("✅ Ana program ba<PERSON>ar<PERSON><PERSON> ta<PERSON>ı!")
        print(f"📁 Dosya: {output_file}")

        # Database statistics
        try:
            total_in_db = db_manager.get_decisions_count()
            print(f"🗄️  Veritabanında toplam karar: {total_in_db}")

            recent = db_manager.get_recent_decisions(5)
            if recent:
                print("📋 Son eklenen kararlar:")
                for decision in recent:
                    print(f"   • {decision.decision_id}: {decision.daire}")
        except Exception as e:
            print(f"❌ Veritabanı istatistik hatası: {e}")

        print("="*60)

    except Exception as e:
        print(f"❌ Ana program hatası: {e}")