import os
from importlib import import_module

if __name__ == "__main__":
    print("Ana program başlıyor...")
    print("Tüm süreci çalıştırıp 100 sayfa veri alacak...")

    # Create output directory if it doesn't exist
    output_dir = "output"
    os.makedirs(output_dir, exist_ok=True)

    # Output file path
    output_file = os.path.join(output_dir, "yargitay_kararlar.jsonl")

    #95941 pages total - running 100 pages for this session
    try:
        save_module = import_module("4-save_for_json")
        save_module.crawl_and_save(start_page=1, end_page=100, output_file=output_file)
        print("✅ Ana program başarıyla tamamlandı!")
        print(f"📁 Veriler şu dosyaya kaydedildi: {output_file}")
    except Exception as e:
        print(f"❌ Ana program hatası: {e}")